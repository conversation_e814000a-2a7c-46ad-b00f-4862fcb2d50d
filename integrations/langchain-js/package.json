{"name": "@braintrust/langchain-js", "version": "0.0.6", "description": "SDK for integrating Braintrust with LangChain.js", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "module": "./dist/index.mjs", "require": "./dist/index.js"}}, "scripts": {"build": "tsup", "watch": "tsup --watch", "clean": "rm -r dist/*", "test": "vitest run"}, "author": "Braintrust Data Inc.", "license": "MIT", "devDependencies": {"@langchain/core": "^0.3.42", "@langchain/langgraph": "^0.2.25", "@langchain/openai": "^0.3.17", "@types/node": "^20.10.5", "msw": "^2.6.6", "tsup": "^8.3.5", "typescript": "^5.3.3", "vitest": "^2.1.9", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.5"}, "dependencies": {"@braintrust/core": "workspace:*", "braintrust": "workspace:^"}, "peerDependencies": {"@langchain/core": "^0.3.42"}}