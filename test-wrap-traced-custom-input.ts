#!/usr/bin/env tsx

import { wrapTraced, initLogger } from "./js/src/logger";

// Initialize Braintrust logger
const logger = initLogger({ 
  projectName: "wrap-traced-custom-input-test",
  // Use a memory logger for testing so we can see the results
});

// Test function 1: Normal behavior (logs actual function arguments)
const normalFunction = wrapTraced(
  async function processData(sensitivePassword: string, username: string, metadata: any) {
    console.log("Processing data for user:", username);
    // Simulate some work
    await new Promise(resolve => setTimeout(resolve, 100));
    return { success: true, user: username, timestamp: Date.now() };
  },
  { name: "normalFunction" }
);

// Test function 2: Custom input (overrides function arguments)
const customInputFunction = wrapTraced(
  async function processData(sensitivePassword: string, username: string, metadata: any) {
    console.log("Processing data for user:", username);
    // Simulate some work
    await new Promise(resolve => setTimeout(resolve, 100));
    return { success: true, user: username, timestamp: Date.now() };
  },
  { 
    name: "customInputFunction",
    event: {
      input: {
        username: "sanitized_username",
        hasPassword: true,
        metadataKeys: ["key1", "key2"]
      }
    }
  }
);

// Test function 3: Custom input with string value
const stringInputFunction = wrapTraced(
  async function processData(sensitivePassword: string, username: string, metadata: any) {
    console.log("Processing data for user:", username);
    // Simulate some work
    await new Promise(resolve => setTimeout(resolve, 100));
    return { success: true, user: username, timestamp: Date.now() };
  },
  { 
    name: "stringInputFunction",
    event: {
      input: "Custom sanitized input description"
    }
  }
);

async function runTests() {
  console.log("=== Testing wrapTraced with custom input ===\n");

  // Test data
  const sensitivePassword = "super-secret-password-123";
  const username = "<EMAIL>";
  const metadata = { 
    sessionId: "sess_abc123", 
    ipAddress: "*************",
    userAgent: "Mozilla/5.0..."
  };

  try {
    console.log("1. Testing normal function (will log actual arguments):");
    const result1 = await normalFunction(sensitivePassword, username, metadata);
    console.log("Result:", result1);
    console.log("✅ Normal function completed\n");

    console.log("2. Testing custom input function (will log custom input object):");
    const result2 = await customInputFunction(sensitivePassword, username, metadata);
    console.log("Result:", result2);
    console.log("✅ Custom input function completed\n");

    console.log("3. Testing string input function (will log custom string):");
    const result3 = await stringInputFunction(sensitivePassword, username, metadata);
    console.log("Result:", result3);
    console.log("✅ String input function completed\n");

    // Flush the logger to ensure all spans are sent
    await logger.flush();
    
    console.log("=== Test completed! ===");
    console.log("Check your Braintrust dashboard to see the difference in logged inputs:");
    console.log("- normalFunction: Will show the actual function arguments including the sensitive password");
    console.log("- customInputFunction: Will show the sanitized input object");
    console.log("- stringInputFunction: Will show the custom string description");

  } catch (error) {
    console.error("Test failed:", error);
  }
}

// Run the tests
runTests().catch(console.error);
