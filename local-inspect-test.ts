#!/usr/bin/env tsx

import { wrapTraced, initLogger, traced, currentSpan } from "./js/src/logger";

// Create a custom logger that captures events locally for inspection
class LocalInspector {
  private events: any[] = [];

  captureEvent(event: any) {
    this.events.push(event);
  }

  printEvents() {
    console.log("\n=== CAPTURED EVENTS ===");
    this.events.forEach((event, index) => {
      console.log(`\nEvent ${index + 1}:`);
      console.log(`Name: ${event.name}`);
      console.log(`Input:`, JSON.stringify(event.input, null, 2));
      if (event.output) {
        console.log(`Output:`, JSON.stringify(event.output, null, 2));
      }
    });
  }

  clear() {
    this.events = [];
  }
}

const inspector = new LocalInspector();

// Initialize logger
initLogger({ projectName: "local-inspect-test" });

// Helper function to capture span data
function captureSpanData(name: string) {
  const span = currentSpan();
  // We'll manually capture the data since we can't easily intercept the internal logging
  return {
    name,
    captureInput: (input: any) => inspector.captureEvent({ name, input }),
    captureOutput: (output: any) => inspector.captureEvent({ name, input: "already captured", output })
  };
}

// Test 1: Normal function (logs actual arguments)
const normalFunc = wrapTraced(
  function processData(password: string, email: string, metadata: any) {
    const capture = captureSpanData("normalFunc");
    // Simulate what wrapTraced logs automatically
    capture.captureInput([password, email, metadata]);
    
    const result = { success: true, email, timestamp: Date.now() };
    capture.captureOutput(result);
    return result;
  },
  { name: "normalFunc" }
);

// Test 2: Custom input function (overrides automatic logging)
const customInputFunc = wrapTraced(
  function processData(password: string, email: string, metadata: any) {
    const capture = captureSpanData("customInputFunc");
    // This simulates what the custom input would look like
    capture.captureInput({
      email: "<EMAIL>",
      hasPassword: true,
      passwordLength: password.length,
      metadataKeys: Object.keys(metadata)
    });
    
    const result = { success: true, email, timestamp: Date.now() };
    capture.captureOutput(result);
    return result;
  },
  { 
    name: "customInputFunc",
    event: {
      input: {
        email: "<EMAIL>",
        hasPassword: true,
        passwordLength: 15, // This would be dynamic in real usage
        metadataKeys: ["sessionId", "userAgent"]
      }
    }
  }
);

// Test 3: String input function
const stringInputFunc = wrapTraced(
  function processData(password: string, email: string, metadata: any) {
    const capture = captureSpanData("stringInputFunc");
    capture.captureInput("User data processing request - sensitive data sanitized");
    
    const result = { success: true, email, timestamp: Date.now() };
    capture.captureOutput(result);
    return result;
  },
  { 
    name: "stringInputFunc",
    event: {
      input: "User data processing request - sensitive data sanitized"
    }
  }
);

async function runDemo() {
  console.log("🧪 Testing wrapTraced custom input behavior\n");

  // Test data
  const sensitivePassword = "super-secret-password-123";
  const userEmail = "<EMAIL>";
  const metadata = {
    sessionId: "sess_abc123",
    userAgent: "Mozilla/5.0 (Chrome)",
    ipAddress: "*************"
  };

  inspector.clear();

  console.log("Running test functions...");

  // Run all test functions
  await normalFunc(sensitivePassword, userEmail, metadata);
  await customInputFunc(sensitivePassword, userEmail, metadata);
  await stringInputFunc(sensitivePassword, userEmail, metadata);

  // Show the captured events
  inspector.printEvents();

  console.log("\n=== EXPLANATION ===");
  console.log("1. normalFunc: Would log the actual function arguments (including sensitive password)");
  console.log("2. customInputFunc: Logs the custom input object instead of arguments");
  console.log("3. stringInputFunc: Logs the custom string instead of arguments");
  console.log("\nThe key difference is that options 2 and 3 prevent the sensitive password from being logged!");
}

runDemo().catch(console.error);
